<?php
require_once 'includes/init.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    redirect('login.php');
}

// Set page title
$page_title = 'لوحة التحكم الرئيسية';

// Include header
include 'includes/header.php';

// إنشاء اتصال قاعدة البيانات
$db = new Database();

// Get statistics
try {
    // إجمالي الفعاليات
    $db->query("SELECT COUNT(*) as count FROM events");
    $result = $db->single();
    $totalEvents = $result['count'] ?: 0;

    // إجمالي المستخدمين
    $db->query("SELECT COUNT(*) as count FROM users");
    $result = $db->single();
    $totalUsers = $result['count'] ?: 0;

    // إجمالي التذاكر
    $db->query("SELECT COUNT(*) as count FROM tickets");
    $result = $db->single();
    $totalTickets = $result['count'] ?: 0;

    // إجمالي المبيعات
    $db->query("SELECT SUM(total_amount) as total FROM orders WHERE payment_status = 'completed'");
    $result = $db->single();
    $totalSales = $result['total'] ?: 0;

    // إحصائيات المواصلات
    $db->query("SHOW TABLES LIKE 'transport_bookings'");
    $transport_table_exists = $db->single();
    
    if ($transport_table_exists) {
        // إجمالي حجوزات المواصلات
        $db->query("SELECT COUNT(*) as count FROM transport_bookings");
        $result = $db->single();
        $totalTransportBookings = $result['count'] ?: 0;

        // إجمالي إيرادات المواصلات
        $db->query("SELECT SUM(total_amount) as total FROM transport_bookings WHERE payment_status = 'confirmed'");
        $result = $db->single();
        $totalTransportRevenue = $result['total'] ?: 0;

        // السائقين النشطين
        $db->query("SELECT COUNT(*) as count FROM transport_drivers WHERE status = 'available' AND is_active = 1");
        $result = $db->single();
        $activeDrivers = $result['count'] ?: 0;

        // الرحلات النشطة
        $db->query("SELECT COUNT(*) as count FROM transport_trips WHERE is_active = 1");
        $result = $db->single();
        $activeTrips = $result['count'] ?: 0;
    } else {
        $totalTransportBookings = 0;
        $totalTransportRevenue = 0;
        $activeDrivers = 0;
        $activeTrips = 0;
    }

    // المبيعات الحديثة
    $db->query("
        SELECT o.*, u.name as user_name, e.title as event_title
        FROM orders o
        JOIN users u ON o.user_id = u.id
        JOIN events e ON o.event_id = e.id
        WHERE o.payment_status = 'completed'
        ORDER BY o.created_at DESC
        LIMIT 5
    ");
    $recentSales = $db->resultSet();

    // المبيعات الشهرية للرسم البياني
    $db->query("
        SELECT DATE_FORMAT(created_at, '%Y-%m') as month, SUM(total_amount) as total
        FROM orders
        WHERE payment_status = 'completed'
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month ASC
        LIMIT 12
    ");
    $monthlySales = $db->resultSet();

    // الفعاليات القادمة
    $db->query("
        SELECT * FROM events 
        WHERE date_time > NOW() 
        ORDER BY date_time ASC 
        LIMIT 5
    ");
    $upcomingEvents = $db->resultSet();

    // المستخدمين الجدد (آخر 30 يوم)
    $db->query("
        SELECT COUNT(*) as count FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
    $result = $db->single();
    $newUsers = $result['count'] ?: 0;

    // الإشعارات غير المقروءة
    $db->query("SHOW TABLES LIKE 'notifications'");
    $notifications_table_exists = $db->single();
    
    if ($notifications_table_exists) {
        $db->query("SELECT COUNT(*) as count FROM notifications WHERE is_read = 0");
        $result = $db->single();
        $unreadNotifications = $result['count'] ?: 0;

        // آخر الإشعارات
        $db->query("
            SELECT * FROM notifications 
            ORDER BY created_at DESC 
            LIMIT 5
        ");
        $recentNotifications = $db->resultSet();
    } else {
        $unreadNotifications = 0;
        $recentNotifications = [];
    }

} catch (Exception $e) {
    error_log("Dashboard Error: " . $e->getMessage());
    $totalEvents = 0;
    $totalUsers = 0;
    $totalTickets = 0;
    $totalSales = 0;
    $totalTransportBookings = 0;
    $totalTransportRevenue = 0;
    $activeDrivers = 0;
    $activeTrips = 0;
    $recentSales = [];
    $monthlySales = [];
    $upcomingEvents = [];
    $newUsers = 0;
    $unreadNotifications = 0;
    $recentNotifications = [];
}
?>

<style>
.dashboard-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    color: white;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.dashboard-card.events {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.dashboard-card.users {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.dashboard-card.tickets {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.dashboard-card.sales {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.dashboard-card.transport {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
}

.dashboard-card.drivers {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: #333;
}

.dashboard-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 10px 0;
}

.dashboard-card i {
    font-size: 2rem;
    opacity: 0.8;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.section-title {
    color: #333;
    font-size: 1.8rem;
    font-weight: bold;
    margin: 30px 0 20px 0;
    border-bottom: 3px solid #667eea;
    padding-bottom: 10px;
}

.data-table {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.data-table table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: #667eea;
    color: white;
    padding: 15px;
    text-align: right;
    font-weight: bold;
}

.data-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
}

.data-table tr:hover {
    background: #f8f9fa;
}

.chart-container {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.notification-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.notification-item.success {
    border-left-color: #28a745;
}

.notification-item.warning {
    border-left-color: #ffc107;
}

.notification-item.error {
    border-left-color: #dc3545;
}
</style>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="text-primary">📊 لوحة التحكم الرئيسية</h1>
        <div class="text-muted">
            <i class="fas fa-clock"></i>
            <?php echo date('Y-m-d H:i:s'); ?>
        </div>
    </div>

    <!-- إحصائيات النظام الأساسية -->
    <div class="stats-grid">
        <div class="dashboard-card events">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-calendar-alt"></i>
                    <h3><?php echo number_format($totalEvents); ?></h3>
                    <p class="mb-0">إجمالي الفعاليات</p>
                </div>
                <div class="text-end">
                    <small>من قاعدة البيانات</small>
                </div>
            </div>
        </div>

        <div class="dashboard-card users">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-users"></i>
                    <h3><?php echo number_format($totalUsers); ?></h3>
                    <p class="mb-0">إجمالي المستخدمين</p>
                </div>
                <div class="text-end">
                    <small>جدد: <?php echo number_format($newUsers); ?></small>
                </div>
            </div>
        </div>

        <div class="dashboard-card tickets">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-ticket-alt"></i>
                    <h3><?php echo number_format($totalTickets); ?></h3>
                    <p class="mb-0">إجمالي التذاكر</p>
                </div>
                <div class="text-end">
                    <small>مباعة</small>
                </div>
            </div>
        </div>

        <div class="dashboard-card sales">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-money-bill-wave"></i>
                    <h3><?php echo formatPrice($totalSales); ?></h3>
                    <p class="mb-0">إجمالي المبيعات</p>
                </div>
                <div class="text-end">
                    <small>مكتملة</small>
                </div>
            </div>
        </div>

        <div class="dashboard-card transport">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-bus"></i>
                    <h3><?php echo number_format($totalTransportBookings); ?></h3>
                    <p class="mb-0">حجوزات المواصلات</p>
                </div>
                <div class="text-end">
                    <small><?php echo formatPrice($totalTransportRevenue); ?></small>
                </div>
            </div>
        </div>

        <div class="dashboard-card drivers">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-id-card"></i>
                    <h3><?php echo number_format($activeDrivers); ?></h3>
                    <p class="mb-0">السائقين النشطين</p>
                </div>
                <div class="text-end">
                    <small>رحلات: <?php echo number_format($activeTrips); ?></small>
                </div>
            </div>
        </div>
    </div>
