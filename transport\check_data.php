<?php
require_once '../config/database.php';

// إنشاء اتصال قاعدة البيانات
$db = new Database();

echo "<h2>فحص البيانات في قاعدة البيانات</h2>";

try {
    // فحص جدول transport_starting_points
    echo "<h3>نقاط الانطلاق (transport_starting_points):</h3>";
    $db->query("SELECT * FROM transport_starting_points");
    $starting_points = $db->resultSet();
    echo "عدد نقاط الانطلاق: " . count($starting_points) . "<br>";
    foreach ($starting_points as $point) {
        echo "- " . $point['name'] . " (ID: " . $point['id'] . ", نشط: " . ($point['is_active'] ? 'نعم' : 'لا') . ")<br>";
    }
    echo "<br>";

    // فحص جدول transport_types
    echo "<h3>أنواع المواصلات (transport_types):</h3>";
    $db->query("SELECT * FROM transport_types");
    $transport_types = $db->resultSet();
    echo "عدد أنواع الموا��لات: " . count($transport_types) . "<br>";
    foreach ($transport_types as $type) {
        echo "- " . $type['name'] . " (ID: " . $type['id'] . ", السعة: " . $type['capacity'] . ")<br>";
    }
    echo "<br>";

    // فحص جدول transport_drivers
    echo "<h3>السائقين (transport_drivers):</h3>";
    $db->query("SELECT * FROM transport_drivers");
    $drivers = $db->resultSet();
    echo "عدد السائقين: " . count($drivers) . "<br>";
    foreach ($drivers as $driver) {
        echo "- " . $driver['name'] . " (ID: " . $driver['id'] . ", الحالة: " . $driver['status'] . ", نشط: " . ($driver['is_active'] ? 'نعم' : 'لا') . ")<br>";
    }
    echo "<br>";

    // فحص جدول transport_trips
    echo "<h3>الرحلات (transport_trips):</h3>";
    $db->query("SELECT * FROM transport_trips");
    $trips = $db->resultSet();
    echo "عدد الرحلات: " . count($trips) . "<br>";
    foreach ($trips as $trip) {
        echo "- رحلة ID: " . $trip['id'] . ", السعر: $" . $trip['price'] . ", المقاعد المتاحة: " . $trip['available_seats'] . "/" . $trip['total_seats'] . ", نشطة: " . ($trip['is_active'] ? 'نعم' : 'لا') . "<br>";
    }
    echo "<br>";

    // فحص جدول transport_bookings
    echo "<h3>الحجوزات (transport_bookings):</h3>";
    $db->query("SELECT * FROM transport_bookings");
    $bookings = $db->resultSet();
    echo "عدد الحجوزات: " . count($bookings) . "<br>";
    foreach ($bookings as $booking) {
        $passenger_name = $booking['passenger_name'] ?? $booking['customer_name'] ?? 'غير محدد';
        $seats = $booking['seats'] ?? $booking['seats_count'] ?? 0;
        echo "- حجز: " . $booking['booking_code'] . ", العميل: " . $passenger_name . ", المقاعد: " . $seats . ", المبلغ: $" . $booking['total_amount'] . ", الحالة: " . $booking['status'] . "<br>";
    }
    echo "<br>";

    // فحص جدول notifications
    echo "<h3>الإشعارات (notifications):</h3>";
    $db->query("SELECT * FROM notifications ORDER BY created_at DESC LIMIT 5");
    $notifications = $db->resultSet();
    echo "عدد الإشعارات (آخر 5): " . count($notifications) . "<br>";
    foreach ($notifications as $notification) {
        echo "- " . $notification['title'] . " (" . $notification['created_at'] . ")<br>";
    }
    echo "<br>";

    // فحص جدول events
    echo "<h3>الفعاليات (events):</h3>";
    $db->query("SELECT * FROM events LIMIT 5");
    $events = $db->resultSet();
    echo "عدد الفعاليات (أول 5): " . count($events) . "<br>";
    foreach ($events as $event) {
        echo "- " . $event['title'] . " (ID: " . $event['id'] . ")<br>";
    }
    echo "<br>";

    // إحصائيات سريعة
    echo "<h3>إحصائيات سريعة:</h3>";
    
    // إجمالي الإيرادات
    $db->query("SELECT SUM(total_amount) as total_revenue FROM transport_bookings WHERE payment_status = 'confirmed'");
    $revenue = $db->single();
    echo "إجمالي الإيرادات: $" . ($revenue['total_revenue'] ?? 0) . "<br>";
    
    // السائقين المتاحين
    $db->query("SELECT COUNT(*) as count FROM transport_drivers WHERE status = 'available' AND is_active = 1");
    $available_drivers = $db->single();
    echo "السائقين المتاحين: " . $available_drivers['count'] . "<br>";
    
    // الرحلات النشطة
    $db->query("SELECT COUNT(*) as count FROM transport_trips WHERE is_active = 1");
    $active_trips = $db->single();
    echo "الرحلات النشطة: " . $active_trips['count'] . "<br>";

} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}

echo "<br><a href='dashboard.php'>العودة إلى لوحة التحكم</a>";
?>