<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🧹 مدير تنظيف المشروع</h1>";

// تصنيف الملفات
$file_categories = [
    'test_files' => [
        'name' => 'ملفات الاختبار والتطوير',
        'description' => 'ملفات آمنة للحذف - تم استخدامها للاختبار فقط',
        'safety' => 'آمن 100%',
        'color' => '#28a745',
        'files' => [
            'test_all_functions.php',
            'test_all_pages.php',
            'test_basic.php',
            'test_booking_flow.php',
            'test_checkout_redirect.php',
            'test_full_flow.php',
            'test_login_redirect.php',
            'test_my_tickets.php',
            'test_notifications.php',
            'test_payment_process.php',
            'test_payment_processing.php',
            'test_process_booking.php',
            'test_simple.php',
            'test_transport_bookings.php',
            'test_transport_checkout.php',
            'test_transport_flow.php',
            'test_transport_integration.php',
            'test_transport_links.php',
            'test_transport_system.php',
            'test_unified_checkout.php',
            'test_website_complete.php',
            'debug_booking.log',
            'debug_payment_process.php',
            'debug_process_booking.php',
            'debug_simple.php',
            'debug_site.php',
            'simulate_booking_form.php',
            'final_website_test.php'
        ]
    ],
    'fix_files' => [
        'name' => 'ملفات الإصلاح والإعداد',
        'description' => 'ملفات تم استخدامها لإصلاح المشاكل - يمكن حذفها بعد التأكد',
        'safety' => 'آمن بعد التأكد',
        'color' => '#ffc107',
        'files' => [
            'add_original_price_column.php',
            'add_trips_for_existing_points.php',
            'check_and_fix_users_table.php',
            'check_db_vs_code.php',
            'check_tables.php',
            'check_trips_table.php',
            'fix_all_pages.php',
            'fix_db_columns.php',
            'fix_duplicate_functions.php',
            'fix_missing_columns.php',
            'fix_notifications_table.php',
            'fix_tickets_table.php',
            'fix_transport_trips.php',
            'setup_admin_permissions.php',
            'setup_notifications.php',
            'setup_notifications_system.php',
            'create_notifications_table.sql',
            'create_sample_transport_data.php',
            'create_super_admin.php',
            'create_transport_table.php',
            'cleanup_project.php',
            'clear_errors.php',
            'remove_telegram_columns.php',
            'update_payment_security.php'
        ]
    ],
    'duplicate_files' => [
        'name' => 'ملفات مكررة أو قديمة',
        'description' => 'ملفات قد تكون مكررة أو غير مستخدمة',
        'safety' => 'يحتاج مراجعة',
        'color' => '#fd7e14',
        'files' => [
            'about-simple.php',
            'payment-processing.php',
            'paypal-loading.php',
            'paypal-login.php',
            'verify-paypal.php',
            'verify-paypal-ajax.php',
            'new1.zip'
        ]
    ],
    'log_files' => [
        'name' => 'ملفات السجلات والمراقبة',
        'description' => 'ملفات مفيدة للمراقبة - يمكن الاحتفاظ بها',
        'safety' => 'مفيد للاحتفاظ',
        'color' => '#17a2b8',
        'files' => [
            'view_debug_log.php',
            'view_error_log.php',
            'logs/error.log',
            'logs/telegram_cleanup_report.json'
        ]
    ]
];

// معالجة الحذف
if (isset($_POST['delete_files']) && isset($_POST['selected_files'])) {
    $selected_files = $_POST['selected_files'];
    $deleted_count = 0;
    $failed_count = 0;
    
    echo "<div style='background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🗑️ نتائج الحذف:</h3>";
    
    foreach ($selected_files as $file) {
        if (file_exists($file)) {
            if (unlink($file)) {
                echo "✅ تم حذف: {$file}<br>";
                $deleted_count++;
            } else {
                echo "❌ فشل حذف: {$file}<br>";
                $failed_count++;
            }
        } else {
            echo "⚠️ الملف غير موجود: {$file}<br>";
        }
    }
    
    echo "<br><strong>الإجمالي:</strong> تم حذف {$deleted_count} ملف، فشل {$failed_count} ملف";
    echo "</div>";
}

?>

<form method="POST" style="margin: 20px 0;">
    
<?php foreach ($file_categories as $category_key => $category): ?>
    <div style="background: white; border-radius: 8px; padding: 20px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <div style="display: flex; align-items: center; margin-bottom: 15px;">
            <div style="width: 20px; height: 20px; background: <?php echo $category['color']; ?>; border-radius: 50%; margin-left: 10px;"></div>
            <h3 style="margin: 0; color: #333;"><?php echo $category['name']; ?></h3>
            <span style="background: <?php echo $category['color']; ?>; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-right: 10px;">
                <?php echo $category['safety']; ?>
            </span>
        </div>
        
        <p style="color: #666; margin-bottom: 15px;"><?php echo $category['description']; ?></p>
        
        <div style="margin-bottom: 15px;">
            <label style="display: flex; align-items: center; cursor: pointer;">
                <input type="checkbox" id="select_all_<?php echo $category_key; ?>" style="margin-left: 8px;">
                <strong>تحديد الكل (<?php echo count($category['files']); ?> ملف)</strong>
            </label>
        </div>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 10px;">
            <?php foreach ($category['files'] as $file): ?>
                <?php 
                $exists = file_exists($file);
                $size = $exists ? filesize($file) : 0;
                $size_text = $exists ? number_format($size / 1024, 1) . ' KB' : 'غير موجود';
                ?>
                <label style="display: flex; align-items: center; padding: 8px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer; <?php echo $exists ? '' : 'opacity: 0.5;'; ?>">
                    <input type="checkbox" name="selected_files[]" value="<?php echo $file; ?>" class="category_<?php echo $category_key; ?>" style="margin-left: 8px;" <?php echo $exists ? '' : 'disabled'; ?>>
                    <div style="flex: 1;">
                        <div style="font-weight: 500;"><?php echo basename($file); ?></div>
                        <div style="font-size: 12px; color: #666;">
                            <?php echo $size_text; ?>
                            <?php if (!$exists): ?>
                                <span style="color: #dc3545;">• غير موجود</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </label>
            <?php endforeach; ?>
        </div>
    </div>
    
    <script>
        document.getElementById('select_all_<?php echo $category_key; ?>').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.category_<?php echo $category_key; ?>');
            checkboxes.forEach(checkbox => {
                if (!checkbox.disabled) {
                    checkbox.checked = this.checked;
                }
            });
        });
    </script>
<?php endforeach; ?>

    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="color: #dc3545;">⚠️ تحذير مهم:</h3>
        <ul style="color: #666;">
            <li>تأكد من عمل الموقع بشكل صحيح قبل الحذف</li>
            <li>اعمل نسخة احتياطية من المشروع</li>
            <li>ابدأ بحذف ملفات الاختبار أولاً</li>
            <li>لا يمكن التراجع عن الحذف</li>
        </ul>
        
        <div style="margin: 20px 0;">
            <button type="submit" name="delete_files" style="background: #dc3545; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;" onclick="return confirm('هل أنت متأكد من رغبتك في حذف الملفات المحددة؟ لا يمكن التراجع عن هذا الإجراء!')">
                🗑️ حذف الملفات المحددة
            </button>
            
            <button type="button" onclick="window.location.href='cleanup_manager.php'" style="background: #6c757d; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin-right: 10px;">
                🔄 إعادة تحميل
            </button>
        </div>
    </div>
</form>

<?php
// إحصائيات المشروع
$total_files = 0;
$total_size = 0;

function scan_directory($dir) {
    global $total_files, $total_size;
    
    if (!is_dir($dir)) return;
    
    $files = scandir($dir);
    foreach ($files as $file) {
        if ($file == '.' || $file == '..') continue;
        
        $path = $dir . '/' . $file;
        if (is_file($path)) {
            $total_files++;
            $total_size += filesize($path);
        } elseif (is_dir($path)) {
            scan_directory($path);
        }
    }
}

scan_directory('.');

echo "<div style='background: #e2e3e5; color: #383d41; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>📊 إحصائيات المشروع:</h3>";
echo "<p><strong>إجمالي الملفات:</strong> " . number_format($total_files) . " ملف</p>";
echo "<p><strong>الحجم الإجمالي:</strong> " . number_format($total_size / 1024 / 1024, 2) . " MB</p>";
echo "</div>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
    direction: rtl;
}

h1, h2, h3 {
    color: #333;
}

h1 {
    border-bottom: 3px solid #6f42c1;
    padding-bottom: 10px;
}

label {
    transition: background-color 0.2s;
}

label:hover {
    background-color: #f8f9fa !important;
}

button {
    transition: all 0.2s;
}

button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}
</style>
