# تعليمات إعداد نظام المواصلات

## المشكلة المحلولة
تم حل مشكلة الخطأ في لوحة تحكم المواصلات:
- **خطأ SQL**: `Not unique table/alias: 'tt'`
- **السبب**: استخدام نفس الاسم المستعار لجدولين مختلفين
- **الحل**: تغيير الاسم المستعار من `tt` إلى `ttype` لجدول `transport_types`

## خطوات الإعداد

### 1. إنشاء جداول قاعدة البيانات

#### الطريقة الأولى: استخدام phpMyAdmin
1. افتح phpMyAdmin من خلال XAMPP
2. اختر قاعدة البيانات `tickets_db`
3. اذهب إلى تبويب "SQL"
4. انسخ محتوى ملف `create_transport_tables.sql` والصقه
5. اضغط "تنفيذ" (Execute)

#### الطريقة الثانية: استخدام سطر الأوامر
```bash
mysql -u root -p tickets_db < create_transport_tables.sql
```

### 2. التحقق من إنشاء الجداول
بعد تنفيذ الملف، يجب أن تجد الجداول التالية:
- `transport_starting_points` - ن��اط الانطلاق
- `transport_types` - أنواع وسائل النقل  
- `transport_drivers` - السائقين
- `transport_vehicles` - المركبات
- `transport_trips` - الرحلات
- `transport_bookings` - حجوزات المواصلات
- `notifications` - الإشعارات

### 3. الوصول إلى لوحة التحكم
بعد إنشاء الجداول، يمكنك الوصول إلى لوحة تحكم المواصلات من خلال:
```
http://localhost/new1/transport/dashboard.php
```

## الميزات المتاحة

### لوحة التحكم الرئيسية
- **نظرة عامة**: إحصائيات الإيرادات والرحلات والحجوزات
- **إدارة نقاط الانطلاق**: إضافة وتعديل نقاط الانطلاق
- **إدارة الرحلات**: جدولة وإدارة الرحلات
- **إدارة الحجوزات**: متابعة حجوزات العملاء
- **إدارة السائقين**: معلومات السائقين والمركبات
- **التحليلات**: رسوم بيانية ومؤشرات الأداء

### البيانات التجريبية المدرجة
- **6 نقاط انطلاق** في مناطق مختلفة من فلسطين
- **4 أنواع مركبات** (حافلة كبيرة، متوسطة، ميكروباص، فان)
- **5 سائقين** مع معلومات كاملة
- **5 مركبات** مربوطة بالسائقين
- **إشعارات تجريبية** لاختبار النظام

## استكشاف الأخطاء

### إذا ظهرت رسالة التنبيه الصفراء
هذا يعني أن الجداول لم يتم إنشاؤها بعد. اتبع خطوات الإعداد أعلاه.

### إذا ظهرت أخطاء SQL أخرى
1. تأكد من وجود جدول `events` و `users` في قاعدة البيانات
2. تأكد من أن قاعدة البيانات تسمى `tickets_db`
3. تحقق من إعدادات الاتصال في ملف `dashboard.php`

### إذا لم تظهر البيانات
1. تأكد من تنفيذ جميع استعلامات INSERT في ملف SQL
2. تحقق من أن الجداول تحتوي على بيانات من خلال phpMyAdmin
3. راجع ملف error log في مجلد `logs`

## الدعم الفني
في حالة مواجهة أي مشاكل، يرجى:
1. التحقق من ملف error log
2. التأكد من تشغيل خدمات XAMPP (Apache + MySQL)
3. التحقق من إعدادات قاعدة البيانات

## ملاحظات مهمة
- تأكد من نسخ احتياطي لقاعدة البيانات قبل تنفيذ أي تغييرات
- الملف يحتوي على `IF NOT EXISTS` لتجنب الأخطاء عند التنفيذ المتكرر
- البيانات التجريبية ستساعد في اختبار النظام فوراً