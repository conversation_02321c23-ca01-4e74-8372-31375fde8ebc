<?php
require_once '../config/database.php';

// إنشاء اتصال قاعدة البيانات
$db = new Database();

// جلب الإحص��ئيات من قاعدة البيانات
try {
    // التحقق من وجود الجداول أولاً
    $db->query("SHOW TABLES LIKE 'transport_bookings'");
    $bookings_table_exists = $db->single();
    
    if ($bookings_table_exists) {
        // إجمالي الإيرادات - تحديث أسماء الأعمدة
        $db->query("SELECT SUM(total_amount) as total_revenue FROM transport_bookings WHERE payment_status = 'confirmed'");
        $revenue_result = $db->single();
        $total_revenue = $revenue_result['total_revenue'] ?? 0;
        
        // الحجوزات الجديدة (آخر 30 يوم)
        $db->query("SELECT COUNT(*) as new_bookings FROM transport_bookings WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
        $bookings_result = $db->single();
        $new_bookings = $bookings_result['new_bookings'] ?? 0;
        
        // الحجوزات - تحديث أسماء الأعمدة
        $db->query("
            SELECT b.*, 
                   COALESCE(b.passenger_name, b.customer_name) as passenger_name,
                   COALESCE(b.seats, b.seats_count) as seats,
                   t.departure_time, 
                   sp.name as starting_point_name, 
                   d.name as driver_name
            FROM transport_bookings b
            LEFT JOIN transport_trips t ON b.trip_id = t.id
            LEFT JOIN transport_starting_points sp ON t.starting_point_id = sp.id
            LEFT JOIN transport_drivers d ON t.driver_id = d.id
            ORDER BY b.created_at DESC
            LIMIT 10
        ");
        $bookings = $db->resultSet();
        
        // بيانات الإيرادات حسب نوع الرحلة
        $db->query("
            SELECT tt.name as trip_type, COUNT(b.id) as bookings_count, SUM(b.total_amount) as revenue
            FROM transport_bookings b
            LEFT JOIN transport_trips t ON b.trip_id = t.id
            LEFT JOIN transport_types tt ON t.transport_type_id = tt.id
            WHERE b.payment_status = 'confirmed'
            GROUP BY tt.id, tt.name
        ");
        $revenue_by_type = $db->resultSet();
    } else {
        $total_revenue = 0;
        $new_bookings = 0;
        $bookings = [];
        $revenue_by_type = [];
    }

    // إجمالي الرحلات
    $db->query("SELECT COUNT(*) as total_trips FROM transport_trips WHERE is_active = 1");
    $trips_result = $db->single();
    $total_trips = $trips_result['total_trips'] ?? 0;

    // السائقين النشطين
    $db->query("SELECT COUNT(*) as active_drivers FROM transport_drivers WHERE status = 'available' AND is_active = 1");
    $drivers_result = $db->single();
    $active_drivers = $drivers_result['active_drivers'] ?? 0;

    // الأنشطة الحديثة
    $db->query("SELECT * FROM notifications ORDER BY created_at DESC LIMIT 5");
    $recent_activities = $db->resultSet();

    // نقاط الانطلاق
    $db->query("SELECT * FROM transport_starting_points ORDER BY name");
    $departure_points = $db->resultSet();

    // الرحلات
    $db->query("
        SELECT t.*, sp.name as starting_point_name, d.name as driver_name, tt.name as transport_type
        FROM transport_trips t
        LEFT JOIN transport_starting_points sp ON t.starting_point_id = sp.id
        LEFT JOIN transport_drivers d ON t.driver_id = d.id
        LEFT JOIN transport_types tt ON t.transport_type_id = tt.id
        ORDER BY t.departure_time DESC
        LIMIT 10
    ");
    $trips = $db->resultSet();

    // السائقين
    $db->query("SELECT * FROM transport_drivers ORDER BY name LIMIT 10");
    $drivers = $db->resultSet();

    // إضافة معلومات تشخيصية
    echo "<!-- Debug Info: 
    Total Revenue: $total_revenue
    Total Trips: $total_trips  
    Active Drivers: $active_drivers
    New Bookings: $new_bookings
    Departure Points: " . count($departure_points) . "
    Trips: " . count($trips) . "
    Drivers: " . count($drivers) . "
    Bookings: " . count($bookings) . "
    -->";

} catch (Exception $e) {
    error_log("Dashboard Error: " . $e->getMessage());
    echo "<!-- Error: " . $e->getMessage() . " -->";
    // قيم افتراضية في حالة الخطأ
    $total_revenue = 0;
    $total_trips = 0;
    $active_drivers = 0;
    $new_bookings = 0;
    $recent_activities = [];
    $departure_points = [];
    $trips = [];
    $drivers = [];
    $bookings = [];
    $revenue_by_type = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Travel Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            100: '#f3e8ff',
                            200: '#e9d5ff',
                            300: '#d8b4fe',
                            400: '#c084fc',
                            500: '#a855f7',
                            600: '#9333ea',
                            700: '#7e22ce',
                            800: '#6b21a8',
                            900: '#581c87',
                        },
                        secondary: {
                            100: '#f0f9ff',
                            200: '#e0f2fe',
                            300: '#bae6fd',
                            400: '#7dd3fc',
                            500: '#38bdf8',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .dashboard-section {
            display: none;
        }
        .dashboard-section.active {
            display: block;
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .nav-item.active {
            border-bottom: 3px solid white;
            font-weight: bold;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Main Container -->
    <div class="flex flex-col min-h-screen">
        <!-- Header -->
        <header class="bg-gradient-to-r from-primary-700 to-primary-900 text-white p-4 shadow-lg">
            <div class="container mx-auto flex justify-between items-center">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-plane-departure text-3xl"></i>
                    <h1 class="text-2xl font-bold">TravelPro Dashboard</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <i class="fas fa-bell text-xl cursor-pointer"></i>
                        <span class="absolute -top-1 -right-1 bg-secondary-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Profile" class="w-8 h-8 rounded-full">
                        <span class="font-medium">Admin</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Horizontal Navigation -->
        <nav class="bg-gradient-to-r from-primary-600 to-primary-800 text-white shadow-md">
            <div class="container mx-auto overflow-x-auto">
                <div class="flex space-x-1 py-2 px-1">
                    <button onclick="showSection('overview')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition active">
                        <i class="fas fa-home mr-2"></i> Overview
                    </button>
                    <button onclick="showSection('revenue')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-chart-line mr-2"></i> Revenue
                    </button>
                    <button onclick="showSection('departure-points')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-map-marker-alt mr-2"></i> Departure Points
                    </button>
                    <button onclick="showSection('trips')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-route mr-2"></i> Trips
                    </button>
                    <button onclick="showSection('drivers')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-id-card-alt mr-2"></i> Drivers
                    </button>
                    <button onclick="showSection('bookings')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-calendar-check mr-2"></i> Bookings
                    </button>
                    <button onclick="showSection('analytics')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-chart-pie mr-2"></i> Analytics
                    </button>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="flex-grow container mx-auto p-4">
            <!-- Overview Section -->
            <section id="overview" class="dashboard-section active">
                <h2 class="text-2xl font-bold text-primary-900 mb-6">Dashboard Overview</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-xl shadow-md p-6 card-hover transition">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500">Total Revenue</p>
                                <h3 class="text-2xl font-bold text-primary-700">$<?php echo number_format($total_revenue, 2); ?></h3>
                                <p class="text-green-500 text-sm mt-1"><i class="fas fa-arrow-up mr-1"></i> من قاعدة البيانات</p>
                            </div>
                            <div class="bg-primary-100 p-3 rounded-full">
                                <i class="fas fa-dollar-sign text-primary-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6 card-hover transition">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500">Total Trips</p>
                                <h3 class="text-2xl font-bold text-secondary-700"><?php echo $total_trips; ?></h3>
                                <p class="text-green-500 text-sm mt-1"><i class="fas fa-arrow-up mr-1"></i> من قاعدة البيانات</p>
                            </div>
                            <div class="bg-secondary-100 p-3 rounded-full">
                                <i class="fas fa-route text-secondary-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6 card-hover transition">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500">Active Drivers</p>
                                <h3 class="text-2xl font-bold text-amber-700"><?php echo $active_drivers; ?></h3>
                                <p class="text-blue-500 text-sm mt-1"><i class="fas fa-database mr-1"></i> من قاعدة البيانات</p>
                            </div>
                            <div class="bg-amber-100 p-3 rounded-full">
                                <i class="fas fa-id-card-alt text-amber-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6 card-hover transition">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500">New Bookings</p>
                                <h3 class="text-2xl font-bold text-emerald-700"><?php echo $new_bookings; ?></h3>
                                <p class="text-green-500 text-sm mt-1"><i class="fas fa-calendar-check mr-1"></i> آخر 30 يوم</p>
                            </div>
                            <div class="bg-emerald-100 p-3 rounded-full">
                                <i class="fas fa-calendar-check text-emerald-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-md p-6 mb-8">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-semibold text-gray-800">Recent Activities</h3>
                        <button class="text-primary-600 hover:text-primary-800">View All</button>
                    </div>
                    <div class="space-y-4">
                        <?php if (!empty($recent_activities)): ?>
                            <?php foreach ($recent_activities as $activity): ?>
                                <div class="flex items-start space-x-4">
                                    <div class="bg-primary-100 p-2 rounded-full">
                                        <?php
                                        $icon = 'fas fa-bell';
                                        $bg_color = 'bg-primary-100';
                                        $text_color = 'text-primary-600';
                                        
                                        switch ($activity['type']) {
                                            case 'success':
                                                $icon = 'fas fa-check-circle';
                                                $bg_color = 'bg-green-100';
                                                $text_color = 'text-green-600';
                                                break;
                                            case 'warning':
                                                $icon = 'fas fa-exclamation-triangle';
                                                $bg_color = 'bg-yellow-100';
                                                $text_color = 'text-yellow-600';
                                                break;
                                            case 'error':
                                                $icon = 'fas fa-times-circle';
                                                $bg_color = 'bg-red-100';
                                                $text_color = 'text-red-600';
                                                break;
                                        }
                                        ?>
                                        <i class="<?php echo $icon . ' ' . $text_color; ?>"></i>
                                    </div>
                                    <div class="flex-grow">
                                        <p class="font-medium"><?php echo htmlspecialchars($activity['title']); ?></p>
                                        <p class="text-gray-500 text-sm"><?php echo htmlspecialchars($activity['message']); ?></p>
                                    </div>
                                    <span class="text-gray-400 text-sm">
                                        <?php 
                                        $time_diff = time() - strtotime($activity['created_at']);
                                        if ($time_diff < 60) {
                                            echo $time_diff . ' ثانية';
                                        } elseif ($time_diff < 3600) {
                                            echo floor($time_diff / 60) . ' دقيقة';
                                        } elseif ($time_diff < 86400) {
                                            echo floor($time_diff / 3600) . ' ساعة';
                                        } else {
                                            echo floor($time_diff / 86400) . ' يوم';
                                        }
                                        ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center text-gray-500 py-4">
                                <i class="fas fa-inbox text-3xl mb-2"></i>
                                <p>لا توجد أنشطة حديثة</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">Revenue Overview</h3>
                        <div class="h-64">
                            <canvas id="revenueChart"></canvas>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">Trip Distribution</h3>
                        <div class="h-64">
                            <canvas id="tripChart"></canvas>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Revenue Section -->
            <section id="revenue" class="dashboard-section">
                <h2 class="text-2xl font-bold text-primary-900 mb-6">Revenue Management</h2>
                <div class="bg-white rounded-xl shadow-md p-6 mb-8">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-semibold text-gray-800">Monthly Revenue</h3>
                        <div class="flex space-x-2">
                            <button class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition">Export</button>
                            <select class="border rounded-lg px-3 py-2">
                                <option>Last 6 Months</option>
                                <option>This Year</option>
                                <option>Last Year</option>
                            </select>
                        </div>
                    </div>
                    <div class="h-96">
                        <canvas id="detailedRevenueChart"></canvas>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">Revenue by Trip Type</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trip Type</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bookings</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Revenue</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (!empty($revenue_by_type)): ?>
                                    <?php foreach ($revenue_by_type as $type): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap"><?php echo htmlspecialchars($type['trip_type'] ?? 'غير محدد'); ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap"><?php echo $type['bookings_count']; ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap">$<?php echo number_format($type['revenue'], 2); ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap">$<?php echo $type['bookings_count'] > 0 ? number_format($type['revenue'] / $type['bookings_count'], 2) : '0.00'; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="4" class="px-6 py-4 text-center text-gray-500">لا توجد بيانات إيرادات متاحة</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Departure Points Section -->
            <section id="departure-points" class="dashboard-section">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-primary-900">Departure Points</h2>
                    <button onclick="openAddDepartureModal()" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition flex items-center">
                        <i class="fas fa-plus mr-2"></i> Add New
                    </button>
                </div>
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-primary-600 text-white">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Location</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Address</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Contact</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (!empty($departure_points)): ?>
                                    <?php foreach ($departure_points as $point): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">DP-<?php echo str_pad($point['id'], 3, '0', STR_PAD_LEFT); ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap"><?php echo htmlspecialchars($point['name']); ?></td>
                                            <td class="px-6 py-4"><?php echo htmlspecialchars($point['description'] ?? 'لا يوجد وصف'); ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap">-</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php if ($point['is_active']): ?>
                                                    <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">نشط</span>
                                                <?php else: ?>
                                                    <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <button class="text-primary-600 hover:text-primary-800 mr-3">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-red-600 hover:text-red-800">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">لا توجد نقاط انطلاق متاحة</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Trips Section -->
            <section id="trips" class="dashboard-section">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-primary-900">Trip Management</h2>
                    <button onclick="openAddTripModal()" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition flex items-center">
                        <i class="fas fa-plus mr-2"></i> Add New Trip
                    </button>
                </div>
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-primary-600 text-white">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Trip ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Route</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Departure</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Arrival</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Price</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Seats</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (!empty($trips)): ?>
                                    <?php foreach ($trips as $trip): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">TR-<?php echo str_pad($trip['id'], 4, '0', STR_PAD_LEFT); ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php echo htmlspecialchars($trip['starting_point_name'] ?? 'غير محدد'); ?>
                                                <?php if ($trip['transport_type']): ?>
                                                    <br><small class="text-gray-500"><?php echo htmlspecialchars($trip['transport_type']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php echo date('M d, h:i A', strtotime($trip['departure_time'])); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php echo $trip['arrival_time'] ? date('M d, h:i A', strtotime($trip['arrival_time'])) : '-'; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">$<?php echo number_format($trip['price'], 2); ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap"><?php echo $trip['available_seats']; ?>/<?php echo $trip['total_seats']; ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php if ($trip['is_active']): ?>
                                                    <?php if ($trip['available_seats'] == 0): ?>
                                                        <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">ممتلئ</span>
                                                    <?php else: ?>
                                                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">نشط</span>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <button class="text-primary-600 hover:text-primary-800 mr-3">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-red-600 hover:text-red-800">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8" class="px-6 py-4 text-center text-gray-500">لا توجد رحلات متاحة</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Drivers Section -->
            <section id="drivers" class="dashboard-section">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-primary-900">Driver Management</h2>
                    <button onclick="openAddDriverModal()" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition flex items-center">
                        <i class="fas fa-plus mr-2"></i> Add New Driver
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php if (!empty($drivers)): ?>
                        <?php foreach ($drivers as $driver): ?>
                            <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                                <div class="p-6">
                                    <div class="flex items-center space-x-4 mb-4">
                                        <div class="w-16 h-16 rounded-full bg-primary-100 flex items-center justify-center">
                                            <i class="fas fa-user text-primary-600 text-2xl"></i>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-semibold"><?php echo htmlspecialchars($driver['name']); ?></h3>
                                            <p class="text-gray-500">Driver ID: DR-<?php echo str_pad($driver['id'], 4, '0', STR_PAD_LEFT); ?></p>
                                        </div>
                                    </div>
                                    <div class="space-y-2">
                                        <div class="flex items-center">
                                            <i class="fas fa-phone-alt text-primary-600 mr-2"></i>
                                            <span><?php echo htmlspecialchars($driver['phone'] ?? 'غير متوفر'); ?></span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-id-card text-primary-600 mr-2"></i>
                                            <span>License: <?php echo htmlspecialchars($driver['license_number'] ?? 'غير متوفر'); ?></span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-car text-primary-600 mr-2"></i>
                                            <span>Vehicle: <?php echo htmlspecialchars($driver['assigned_vehicle'] ?? 'غير محدد'); ?></span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-star text-primary-600 mr-2"></i>
                                            <span>Rating: <?php echo number_format($driver['rating'], 1); ?>/5.0</span>
                                        </div>
                                    </div>
                                    <div class="mt-4 pt-4 border-t border-gray-100 flex justify-between">
                                        <?php
                                        $status_class = 'bg-gray-100 text-gray-800';
                                        $status_text = $driver['status'];
                                        
                                        switch ($driver['status']) {
                                            case 'available':
                                                $status_class = 'bg-green-100 text-green-800';
                                                $status_text = 'متاح';
                                                break;
                                            case 'busy':
                                                $status_class = 'bg-yellow-100 text-yellow-800';
                                                $status_text = 'مشغول';
                                                break;
                                            case 'offline':
                                                $status_class = 'bg-red-100 text-red-800';
                                                $status_text = 'غير متصل';
                                                break;
                                        }
                                        ?>
                                        <span class="px-3 py-1 <?php echo $status_class; ?> rounded-full text-sm"><?php echo $status_text; ?></span>
                                        <div>
                                            <button class="text-primary-600 hover:text-primary-800 mr-3">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-red-600 hover:text-red-800">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="col-span-full text-center text-gray-500 py-8">
                            <i class="fas fa-users text-4xl mb-4"></i>
                            <p>لا يوجد سائقين متاحين</p>
                        </div>
                    <?php endif; ?>
                </div>
            </section>

            <!-- Bookings Section -->
            <section id="bookings" class="dashboard-section">
                <h2 class="text-2xl font-bold text-primary-900 mb-6">Booking Management</h2>
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-primary-600 text-white">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Booking ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Trip</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Seats</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Total</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (!empty($bookings)): ?>
                                    <?php foreach ($bookings as $booking): ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap"><?php echo htmlspecialchars($booking['booking_code']); ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap"><?php echo htmlspecialchars($booking['passenger_name'] ?? 'غير محدد'); ?></td>
                                            <td class="px-6 py-4">
                                                <?php echo htmlspecialchars($booking['starting_point_name'] ?? 'غير محدد'); ?>
                                                <?php if ($booking['driver_name']): ?>
                                                    <br><small class="text-gray-500">السائق: <?php echo htmlspecialchars($booking['driver_name']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php echo $booking['departure_time'] ? date('M d, Y', strtotime($booking['departure_time'])) : date('M d, Y', strtotime($booking['created_at'])); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap"><?php echo $booking['seats']; ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap">$<?php echo number_format($booking['total_amount'], 2); ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php
                                                $status_class = 'bg-gray-100 text-gray-800';
                                                $status_text = $booking['status'];
                                                
                                                switch ($booking['status']) {
                                                    case 'confirmed':
                                                        $status_class = 'bg-green-100 text-green-800';
                                                        $status_text = 'مؤكد';
                                                        break;
                                                    case 'pending':
                                                        $status_class = 'bg-blue-100 text-blue-800';
                                                        $status_text = 'في الانتظار';
                                                        break;
                                                    case 'cancelled':
                                                        $status_class = 'bg-red-100 text-red-800';
                                                        $status_text = 'ملغي';
                                                        break;
                                                    case 'completed':
                                                        $status_class = 'bg-purple-100 text-purple-800';
                                                        $status_text = 'مكتمل';
                                                        break;
                                                }
                                                ?>
                                                <span class="px-2 py-1 text-xs rounded-full <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">لا توجد حجوزات متاحة</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-gray-500">
                                Showing <span class="font-medium">1</span> to <span class="font-medium">5</span> of <span class="font-medium">24</span> bookings
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50">
                                    Previous
                                </button>
                                <button class="px-3 py-1 border rounded-md text-sm bg-primary-600 text-white hover:bg-primary-700">
                                    1
                                </button>
                                <button class="px-3 py-1 border rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50">
                                    2
                                </button>
                                <button class="px-3 py-1 border rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50">
                                    3
                                </button>
                                <button class="px-3 py-1 border rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50">
                                    Next
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Analytics Section -->
            <section id="analytics" class="dashboard-section">
                <h2 class="text-2xl font-bold text-primary-900 mb-6">Analytics Dashboard</h2>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">Booking Trends</h3>
                        <div class="h-80">
                            <canvas id="bookingTrendsChart"></canvas>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">Revenue by Route</h3>
                        <div class="h-80">
                            <canvas id="revenueByRouteChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">Customer Demographics</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                            <h4 class="text-lg font-medium text-gray-700 mb-3">Age Distribution</h4>
                            <div class="h-64">
                                <canvas id="ageDistributionChart"></canvas>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-lg font-medium text-gray-700 mb-3">Gender Distribution</h4>
                            <div class="h-64">
                                <canvas id="genderDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="bg-gray-100 border-t border-gray-200 py-4">
            <div class="container mx-auto px-4 text-center text-gray-500 text-sm">
                <p>© 2023 TravelPro Dashboard. All rights reserved.</p>
            </div>
        </footer>
    </div>

    <!-- Add Departure Point Modal -->
    <div id="addDepartureModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-md">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold text-gray-800">Add New Departure Point</h3>
                    <button onclick="closeAddDepartureModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Location Name</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500" rows="2"></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Contact Number</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option>Active</option>
                                <option>Maintenance</option>
                                <option>Closed</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeAddDepartureModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">
                            Save
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Trip Modal -->
    <div id="addTripModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-md">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold text-gray-800">Add New Trip</h3>
                    <button onclick="closeAddTripModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Route</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option>New York to Boston</option>
                                <option>Los Angeles to San Francisco</option>
                                <option>Chicago to Detroit</option>
                                <option>Miami to Orlando</option>
                            </select>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Departure Date</label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Departure Time</label>
                                <input type="time" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Price</label>
                            <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Total Seats</label>
                            <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Driver</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option>John Smith</option>
                                <option>Sarah Johnson</option>
                                <option>Michael Brown</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeAddTripModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">
                            Save
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Driver Modal -->
    <div id="addDriverModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-md">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold text-gray-800">Add New Driver</h3>
                    <button onclick="closeAddDriverModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form>
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input type="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                            <input type="tel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Driver License</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Assigned Vehicle</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option>Ford Transit (ABC123)</option>
                                <option>Mercedes Sprinter (XYZ789)</option>
                                <option>Chevrolet Express (DEF456)</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeAddDriverModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">
                            Save
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Navigation functionality
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.dashboard-section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionId).classList.add('active');
            
            // Update active nav item
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Find the button that was clicked and make it active
            const buttons = document.querySelectorAll('.nav-item');
            buttons.forEach(button => {
                if (button.getAttribute('onclick') === `showSection('${sectionId}')`) {
                    button.classList.add('active');
                }
            });
            
            // Initialize charts when section is shown
            if (sectionId === 'overview') {
                initOverviewCharts();
            } else if (sectionId === 'revenue') {
                initRevenueCharts();
            } else if (sectionId === 'analytics') {
                initAnalyticsCharts();
            }
        }

        // Modal functions
        function openAddDepartureModal() {
            document.getElementById('addDepartureModal').classList.remove('hidden');
        }

        function closeAddDepartureModal() {
            document.getElementById('addDepartureModal').classList.add('hidden');
        }

        function openAddTripModal() {
            document.getElementById('addTripModal').classList.remove('hidden');
        }

        function closeAddTripModal() {
            document.getElementById('addTripModal').classList.add('hidden');
        }

        function openAddDriverModal() {
            document.getElementById('addDriverModal').classList.remove('hidden');
        }

        function closeAddDriverModal() {
            document.getElementById('addDriverModal').classList.add('hidden');
        }

        // Chart initialization functions
        function initOverviewCharts() {
            // Revenue Chart
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Revenue',
                        data: [12000, 19000, 15000, 20000, 22000, 24780],
                        backgroundColor: 'rgba(168, 85, 247, 0.1)',
                        borderColor: 'rgba(168, 85, 247, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Trip Chart
            const tripCtx = document.getElementById('tripChart').getContext('2d');
            new Chart(tripCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Standard', 'Premium', 'Luxury'],
                    datasets: [{
                        data: [124, 56, 7],
                        backgroundColor: [
                            'rgba(168, 85, 247, 0.7)',
                            'rgba(56, 189, 248, 0.7)',
                            'rgba(245, 158, 11, 0.7)'
                        ],
                        borderColor: [
                            'rgba(168, 85, 247, 1)',
                            'rgba(56, 189, 248, 1)',
                            'rgba(245, 158, 11, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function initRevenueCharts() {
            // Detailed Revenue Chart
            const detailedRevenueCtx = document.getElementById('detailedRevenueChart').getContext('2d');
            new Chart(detailedRevenueCtx, {
                type: 'bar',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [
                        {
                            label: 'Standard',
                            data: [8000, 12000, 9000, 11000, 13000, 12450],
                            backgroundColor: 'rgba(168, 85, 247, 0.7)'
                        },
                        {
                            label: 'Premium',
                            data: [3000, 5000, 4000, 7000, 6000, 8750],
                            backgroundColor: 'rgba(56, 189, 248, 0.7)'
                        },
                        {
                            label: 'Luxury',
                            data: [1000, 2000, 2000, 2000, 3000, 3580],
                            backgroundColor: 'rgba(245, 158, 11, 0.7)'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            stacked: false,
                        },
                        y: {
                            stacked: false,
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function initAnalyticsCharts() {
            // Booking Trends Chart
            const bookingTrendsCtx = document.getElementById('bookingTrendsChart').getContext('2d');
            new Chart(bookingTrendsCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [
                        {
                            label: 'Bookings',
                            data: [45, 78, 62, 89, 102, 120],
                            borderColor: 'rgba(168, 85, 247, 1)',
                            backgroundColor: 'rgba(168, 85, 247, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'Cancellations',
                            data: [5, 8, 12, 6, 9, 7],
                            borderColor: 'rgba(239, 68, 68, 1)',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Revenue by Route Chart
            const revenueByRouteCtx = document.getElementById('revenueByRouteChart').getContext('2d');
            new Chart(revenueByRouteCtx, {
                type: 'bar',
                data: {
                    labels: ['NY-Boston', 'LA-SF', 'Chicago-Detroit', 'Miami-Orlando'],
                    datasets: [{
                        label: 'Revenue',
                        data: [12450, 8750, 3000, 1770],
                        backgroundColor: [
                            'rgba(168, 85, 247, 0.7)',
                            'rgba(56, 189, 248, 0.7)',
                            'rgba(245, 158, 11, 0.7)',
                            'rgba(16, 185, 129, 0.7)'
                        ],
                        borderColor: [
                            'rgba(168, 85, 247, 1)',
                            'rgba(56, 189, 248, 1)',
                            'rgba(245, 158, 11, 1)',
                            'rgba(16, 185, 129, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Age Distribution Chart
            const ageDistributionCtx = document.getElementById('ageDistributionChart').getContext('2d');
            new Chart(ageDistributionCtx, {
                type: 'bar',
                data: {
                    labels: ['18-24', '25-34', '35-44', '45-54', '55+'],
                    datasets: [{
                        label: 'Customers',
                        data: [15, 45, 30, 25, 10],
                        backgroundColor: 'rgba(168, 85, 247, 0.7)',
                        borderColor: 'rgba(168, 85, 247, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Gender Distribution Chart
            const genderDistributionCtx = document.getElementById('genderDistributionChart').getContext('2d');
            new Chart(genderDistributionCtx, {
                type: 'pie',
                data: {
                    labels: ['Male', 'Female', 'Other'],
                    datasets: [{
                        data: [55, 40, 5],
                        backgroundColor: [
                            'rgba(56, 189, 248, 0.7)',
                            'rgba(244, 114, 182, 0.7)',
                            'rgba(16, 185, 129, 0.7)'
                        ],
                        borderColor: [
                            'rgba(56, 189, 248, 1)',
                            'rgba(244, 114, 182, 1)',
                            'rgba(16, 185, 129, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Initialize overview charts on page load
        document.addEventListener('DOMContentLoaded', function() {
            initOverviewCharts();
        });
    </script>
</body>
</html>