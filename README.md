# Palestine Event Tickets

نظام حجز تذاكر الفعاليات متعدد اللغات (العربية، الإنجليزية، العبرية)

## نظرة عامة

نظام حجز تذاكر الفعاليات هو منصة ويب متكاملة تتيح للمستخدمين استعراض الفعاليات المختلفة، وحجز التذاكر، ودفع ثمنها عبر الإنترنت. يدعم النظام ثلاث لغات (العربية، الإنجليزية، العبرية) مع دعم كامل لاتجاه الكتابة من اليمين إلى اليسار (RTL) للغتين العربية والعبرية.

## الميزات الرئيسية

- **دعم متعدد اللغات**: العربية، الإنجليزية، العبرية
- **دعم اتجاه الكتابة RTL/LTR**: تصميم متوافق مع اللغات التي تكتب من اليمين إلى اليسار
- **إدارة الفعاليات**: إضافة، تعديل، حذف الفعاليات
- **نظام حجز التذاكر**: حجز التذاكر ودفع ثمنها عبر الإنترنت
- **نظام الدفع**: معالجة معلومات بطاقة الائتمان
- **رموز الخصم**: إنشاء وتطبيق رموز الخصم
- **لوحة تحكم المدير**: إدارة الفعاليات، المستخدمين، التذاكر، والخصومات
- **تكامل Telegram**: إرسال إشعارات للمدير عند إتمام عملية شراء جديدة
- **تصميم متجاوب**: يعمل على جميع الأجهزة (الهواتف، الأجهزة اللوحية، أجهزة الكمبيوتر)

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache، Nginx)
- متصفح ويب حديث

## التثبيت

1. قم بتنزيل أو استنساخ المشروع إلى مجلد الخادم الخاص بك
2. قم بإنشاء قاعدة بيانات جديدة وقم باستيراد ملف `database.sql`
3. قم بتعديل ملف `config/config.php` لتعيين إعدادات الموقع وقاعدة البيانات
4. قم بتعديل ملف `config/database.php` لتعيين معلومات الاتصال بقاعدة البيانات
5. قم بإنشاء مجلد `logs` في المجلد الرئيسي للمشروع وتأكد من أنه قابل للكتابة
6. قم بإنشاء مجلد `assets/uploads` وتأكد من أنه قابل للكتابة

## هيكل المشروع

```
/                                  # المجلد الرئيسي للمشروع
│
├── admin/                         # مجلد لوحة الإدارة
│   ├── index.php                  # الصفحة الرئيسية للوحة الإدارة
│   ├── events.php                 # إدارة الفعاليات (إضافة/تعديل/حذف)
│   ├── users.php                  # إدارة المستخدمين
│   ├── tickets.php                # إدارة التذاكر والمبيعات
│   └── discounts.php              # إدارة رموز الخصم
│
├── assets/                        # مجلد الملفات الثابتة
│   ├── css/                       # ملفات CSS
│   ├── js/                        # ملفات JavaScript
│   ├── img/                       # الصور
│   └── uploads/                   # مجلد تحميل الصور (للفعاليات)
│
├── config/                        # مجلد الإعدادات
│   ├── config.php                 # إعدادات عامة للموقع
│   └── database.php               # إعدادات الاتصال بقاعدة البيانات
│
├── includes/                      # مجلد الملفات المشتركة
│   ├── init.php                   # ملف التهيئة
│   ├── functions.php              # دوال مساعدة عامة
│   ├── auth.php                   # دوال المصادقة
│   ├── header.php                 # رأس الصفحة المشترك
│   └── footer.php                 # تذييل الصفحة المشترك
│
├── lang/                          # مجلد ملفات اللغات
│   ├── ar.php                     # ملف اللغة العربية
│   ├── en.php                     # ملف اللغة الإنجليزية
│   └── he.php                     # ملف اللغة العبرية
│
├── logs/                          # مجلد سجلات الأخطاء
│   └── error.log                  # ملف سجل الأخطاء
│
├── index.php                      # الصفحة الرئيسية للموقع
├── events.php                     # صفحة عرض جميع الفعاليات
├── event-details.php              # صفحة تفاصيل فعالية محددة
├── about.php                      # صفحة من نحن
├── contact.php                    # صفحة اتصل بنا
├── login.php                      # صفحة تسجيل الدخول
├── register.php                   # صفحة إنشاء حساب جديد
├── logout.php                     # صفحة تسجيل الخروج
├── profile.php                    # صفحة الملف الشخصي
├── my-tickets.php                 # صفحة عرض تذاكر المستخدم
├── checkout.php                   # صفحة إتمام عملية الشراء
├── payment-success.php            # صفحة نجاح عملية الدفع
├── forgot-password.php            # صفحة استعادة كلمة المرور
└── reset-password.php             # صفحة إعادة تعيين كلمة المرور
```

## بيانات تسجيل الدخول الافتراضية

### حساب المدير
- البريد الإلكتروني: <EMAIL>
- كلمة المرور: password

## تكوين Telegram

لتفعيل إشعارات Telegram، قم بتعديل ملف `config/config.php` وأضف:

```php
define('TELEGRAM_BOT_TOKEN', 'YOUR_BOT_TOKEN');
define('TELEGRAM_CHAT_ID', 'YOUR_CHAT_ID');
```

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. قم بعمل fork للمشروع
2. قم بإنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. قم بإجراء التغييرات الخاصة بك
4. قم بعمل commit للتغييرات (`git commit -m 'Add some amazing feature'`)
5. قم بدفع التغييرات إلى الفرع (`git push origin feature/amazing-feature`)
6. قم بفتح طلب سحب (Pull Request)

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## الاتصال

إذا كان لديك أي أسئلة أو استفسارات، يرجى التواصل معنا عبر البريد الإلكتروني: <EMAIL>
