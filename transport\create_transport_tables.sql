-- إنشاء جداول نظام المواصلات
-- Transport System Database Tables

-- جدول نقاط الانطلاق
CREATE TABLE IF NOT EXISTS `transport_starting_points` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `region` enum('north','center','south') DEFAULT 'center',
  `icon` varchar(50) DEFAULT 'city',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول أنواع وسائل النقل
CREATE TABLE IF NOT EXISTS `transport_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `capacity` int(11) DEFAULT 20,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول السائقين
CREATE TABLE IF NOT EXISTS `transport_drivers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `phone` varchar(20),
  `license_number` varchar(50),
  `experience_years` int(11) DEFAULT 0,
  `rating` decimal(3,2) DEFAULT 5.00,
  `status` enum('available','busy','offline') DEFAULT 'available',
  `assigned_vehicle` varchar(50),
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المركبات
CREATE TABLE IF NOT EXISTS `transport_vehicles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `driver_id` int(11),
  `transport_type_id` int(11),
  `vehicle_number` varchar(50),
  `model` varchar(100),
  `year` int(11),
  `capacity` int(11) DEFAULT 20,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `driver_id` (`driver_id`),
  KEY `transport_type_id` (`transport_type_id`),
  FOREIGN KEY (`driver_id`) REFERENCES `transport_drivers` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`transport_type_id`) REFERENCES `transport_types` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الرحلات
CREATE TABLE IF NOT EXISTS `transport_trips` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_id` int(11),
  `starting_point_id` int(11),
  `transport_type_id` int(11),
  `driver_id` int(11),
  `vehicle_id` int(11),
  `departure_time` datetime NOT NULL,
  `arrival_time` datetime,
  `price` decimal(10,2) NOT NULL,
  `total_seats` int(11) DEFAULT 20,
  `available_seats` int(11) DEFAULT 20,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `event_id` (`event_id`),
  KEY `starting_point_id` (`starting_point_id`),
  KEY `transport_type_id` (`transport_type_id`),
  KEY `driver_id` (`driver_id`),
  KEY `vehicle_id` (`vehicle_id`),
  FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`starting_point_id`) REFERENCES `transport_starting_points` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`transport_type_id`) REFERENCES `transport_types` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`driver_id`) REFERENCES `transport_drivers` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`vehicle_id`) REFERENCES `transport_vehicles` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول حجوزات المواصلات
CREATE TABLE IF NOT EXISTS `transport_bookings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `booking_code` varchar(20) UNIQUE NOT NULL,
  `user_id` int(11),
  `event_id` int(11),
  `trip_id` int(11),
  `passenger_name` varchar(255) NOT NULL,
  `passenger_phone` varchar(20) NOT NULL,
  `passenger_email` varchar(255),
  `seats` int(11) DEFAULT 1,
  `total_amount` decimal(10,2) NOT NULL,
  `payment_method` enum('cash','bank_transfer','jawwal_pay','credit_card') DEFAULT 'cash',
  `payment_status` enum('pending','confirmed','failed','refunded') DEFAULT 'pending',
  `status` enum('pending','confirmed','cancelled','completed') DEFAULT 'pending',
  `notes` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `event_id` (`event_id`),
  KEY `trip_id` (`trip_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`trip_id`) REFERENCES `transport_trips` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الإشعارات (إذا لم يكن موجوداً)
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11),
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` enum('info','success','warning','error') DEFAULT 'info',
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج بيانات تجريبية

-- نقاط الانطلاق
INSERT INTO `transport_starting_points` (`name`, `description`, `region`, `icon`) VALUES
('مدينة غزة', 'وسط مدينة غزة - ميدان الجندي المجهول', 'center', 'city'),
('جباليا', 'مخيم جباليا - المحطة الرئيسية', 'north', 'camp'),
('رفح', 'مدينة رفح - المعبر الحدودي', 'south', 'border-crossing'),
('خان يونس', 'مدينة خان يونس - الساحة المركزية', 'south', 'city'),
('بيت لاهيا', 'بيت لاهيا - المنطقة الزراعية', 'north', 'farm'),
('دير البلح', 'مدينة دير البلح - وسط القطاع', 'center', 'palm-tree');

-- أنواع وسائل النقل
INSERT INTO `transport_types` (`name`, `description`, `capacity`) VALUES
('حافلة كبيرة', 'حافلة سياحية مكيفة بسعة 45 راكب', 45),
('حافلة متوسطة', 'حافلة مكيفة بسعة 25 راكب', 25),
('ميكروباص', 'ميكروباص مكيف بسعة 14 راكب', 14),
('فان', 'فان مكيف بسعة 8 ركاب', 8);

-- السائقين
INSERT INTO `transport_drivers` (`name`, `phone`, `license_number`, `experience_years`, `rating`, `assigned_vehicle`) VALUES
('محمد الأحمد', '**********', 'DL001', 8, 4.8, 'B-101'),
('أحمد السعدي', '**********', 'DL002', 5, 4.6, 'B-102'),
('خالد النجار', '**********', 'DL003', 12, 4.9, 'B-103'),
('يوسف حسن', '**********', 'DL004', 6, 4.7, 'M-201'),
('عمر الزهار', '**********', 'DL005', 10, 4.5, 'M-202');

-- المركبات
INSERT INTO `transport_vehicles` (`driver_id`, `transport_type_id`, `vehicle_number`, `model`, `year`, `capacity`) VALUES
(1, 2, 'B-101', 'Mercedes Sprinter', 2020, 25),
(2, 2, 'B-102', 'Iveco Daily', 2019, 25),
(3, 1, 'B-103', 'Mercedes Tourismo', 2021, 45),
(4, 3, 'M-201', 'Ford Transit', 2020, 14),
(5, 3, 'M-202', 'Hyundai H350', 2019, 14);

-- إشعارات تجريبية
INSERT INTO `notifications` (`user_id`, `title`, `message`, `type`) VALUES
(NULL, 'حجز جديد', 'تم استلام حجز جديد للمواصلات', 'info'),
(NULL, 'رحلة ملغاة', 'تم إلغاء رحلة غزة - رام الله', 'warning'),
(NULL, 'دفعة مؤكدة', 'تم تأكيد دفعة حجز المواصلات', 'success');