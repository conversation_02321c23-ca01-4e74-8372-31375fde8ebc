# نظام حجز المواصلات للفعاليات

## نظرة عامة
نظام متكامل لحجز المواصلات للفعاليات يتضمن:
- اختيار نقطة الانطلاق
- عرض الرحلات المتاحة مع تفاصيل السائق والمركبة
- نموذج حجز مع التحقق من البيانات
- طرق دفع متعددة
- تأكيد الحجز وإدارة الحجوزات

## الملفات المطلوبة

### 1. قاعدة البيانات
- `create_transport_tables.sql` - إنشاء الجداول المطلوبة

### 2. الدوال
- `includes/transport_functions.php` - دوال نظام المواصلات

### 3. صفحات النظام
- `transport/starting_points.php` - اختيار نقطة الانطلاق
- `transport/trips.php` - عرض الرحلات المتاحة
- `transport/booking_details.php` - تفاصيل الحجز ونموذج البيانات
- `transport/payment_method.php` - اختيار طريقة الدفع
- `transport/confirmation_booking.php` - تأكيد الحجز

### 4. معالجة البيانات
- `transport/process_booking.php` - معالجة بيانات الحجز
- `transport/process_payment.php` - معالجة الدفع وإنشاء الحجز

## خطوات التشغيل

### 1. إعداد قاعدة البيانات
```sql
-- تشغيل ملف SQL لإنشاء الجداول
SOURCE transport/create_transport_tables.sql;
```

### 2. تحديث صفحة تفاصيل الفعالية
تم إضافة زر "اختار مواصلات للفعالية" في `event-details.php`

### 3. تحديث صفحة الحجوزات
تم إضافة قسم حجوزات المواصلات في `my-tickets.php`

## مسار المستخدم

1. **صفحة تفاصيل الفعالية**: المستخدم يضغط على "اختار مواصلات للفعالية"
2. **اختيار نقطة الانطلاق**: يختار المستخدم نقطة الانطلاق من القائمة
3. **عرض الرحلات**: يرى الرحلات المتاحة مع تفاصيل السائق والمركبة
4. **تفاصيل الحجز**: يدخل بياناته الشخصية وعدد الركاب
5. **طريقة الدفع**: يختار طريقة الدفع ويدخل التفاصيل
6. **التأكيد**: يحصل على رقم الحجز وتأكيد العملية

## المميزات

### تفاصيل السائق والمركبة
- معلومات السائق (الاسم، التقييم، سنوات الخبرة)
- تفاصيل المركبة (النوع، الموديل، رقم اللوحة)
- صور السائق والمركبة

### طرق الدفع المتعددة
- التحويل البنكي
- الدفع نقداً عند الركوب
- جوال باي
- بطاقة ائتمان

### إدارة الحجوزات
- رقم حجز فريد لكل حجز
- تتبع حالة الحجز (في الانتظار، مؤكد، ملغي، مكتمل)
- عرض جميع الحجوزات في صفحة واحدة

## الأمان والتحقق

- التحقق من CSRF tokens
- تنظيف البيانات المدخلة
- التحقق من توفر المقاعد
- التحقق من صحة أرقام الجوال
- حماية من SQL Injection

## قاعدة البيانات

### الجداول الرئيسية
1. `transport_starting_points` - نقاط الانطلاق
2. `transport_types` - أنواع وسائل النقل
3. `transport_drivers` - السائقين
4. `transport_vehicles` - المركبات
5. `transport_trips` - الرحلات
6. `transport_bookings` - الحجوزات

### العلاقات
- كل رحلة مرتبطة بفعالية ونقطة انطلاق ومركبة
- كل مركبة مرتبطة بسائق ونوع وسيلة نقل
- كل حجز مرتبط برحلة وفعالية ومستخدم

## التخصيص

يمكن تخصيص النظام من خلال:
- إضافة نقاط انطلاق جديدة
- إضافة أنواع وسائل نقل جديدة
- تعديل طرق الدفع
- تخصيص التصميم والألوان

## الدعم الفني

للاستفسارات والدعم الفني، يرجى التواصل مع فريق التطوير.
