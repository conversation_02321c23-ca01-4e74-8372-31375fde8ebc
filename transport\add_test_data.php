<?php
require_once '../config/database.php';

// إنشاء اتصال قاعدة البيانات
$db = new Database();

echo "<h2>إضافة بيانات تجريبية</h2>";

try {
    // إضافة نقاط انطلاق إذا لم تكن موجودة
    $db->query("SELECT COUNT(*) as count FROM transport_starting_points");
    $points_count = $db->single()['count'];
    
    if ($points_count == 0) {
        echo "إضافة نقاط الانطلاق...<br>";
        $starting_points = [
            ['مدينة غزة', 'وسط مدينة غزة - ميدان الجندي المجهول', 'center'],
            ['جباليا', 'مخيم جباليا - المحطة الرئيسية', 'north'],
            ['رفح', 'مدينة رفح - المعبر الحدودي', 'south'],
            ['خان يونس', 'مدينة خان يونس - الساحة المركزية', 'south'],
            ['بيت لاهيا', 'بيت لاهيا - المنطقة الزراعية', 'north']
        ];
        
        foreach ($starting_points as $point) {
            $db->query("INSERT INTO transport_starting_points (name, description, region, is_active) VALUES (:name, :description, :region, 1)");
            $db->bind(':name', $point[0]);
            $db->bind(':description', $point[1]);
            $db->bind(':region', $point[2]);
            $db->execute();
        }
        echo "تم إضافة " . count($starting_points) . " نقطة انطلاق<br>";
    } else {
        echo "نقاط الانطلاق موجودة بالفعل ($points_count نقطة)<br>";
    }

    // إضافة أنواع المواصلات إذا لم تكن موجودة
    $db->query("SELECT COUNT(*) as count FROM transport_types");
    $types_count = $db->single()['count'];
    
    if ($types_count == 0) {
        echo "إضافة أنواع المواصلات...<br>";
        $transport_types = [
            ['حافلة كبيرة', 'حافلة سياحية مكيفة بسعة 45 راكب', 45],
            ['حافلة متوسطة', 'حافلة مكيفة بسعة 25 راكب', 25],
            ['ميكروباص', 'ميكروباص مك��ف بسعة 14 راكب', 14],
            ['فان', 'فان مكيف بسعة 8 ركاب', 8]
        ];
        
        foreach ($transport_types as $type) {
            $db->query("INSERT INTO transport_types (name, description, capacity, is_active) VALUES (:name, :description, :capacity, 1)");
            $db->bind(':name', $type[0]);
            $db->bind(':description', $type[1]);
            $db->bind(':capacity', $type[2]);
            $db->execute();
        }
        echo "تم إضافة " . count($transport_types) . " نوع مواصلات<br>";
    } else {
        echo "أنواع المواصلات موجودة بالفعل ($types_count نوع)<br>";
    }

    // إضافة السائقين إذا لم يكونوا موجودين
    $db->query("SELECT COUNT(*) as count FROM transport_drivers");
    $drivers_count = $db->single()['count'];
    
    if ($drivers_count == 0) {
        echo "إضافة السائقين...<br>";
        $drivers = [
            ['محمد الأحمد', '**********', 'DL001', 8, 4.8, 'B-101'],
            ['أحمد السعدي', '**********', 'DL002', 5, 4.6, 'B-102'],
            ['خالد النجار', '**********', 'DL003', 12, 4.9, 'B-103'],
            ['يوسف حسن', '**********', 'DL004', 6, 4.7, 'M-201'],
            ['عمر الزهار', '**********', 'DL005', 10, 4.5, 'M-202']
        ];
        
        foreach ($drivers as $driver) {
            $db->query("INSERT INTO transport_drivers (name, phone, license_number, experience_years, rating, assigned_vehicle, status, is_active) VALUES (:name, :phone, :license_number, :experience_years, :rating, :assigned_vehicle, 'available', 1)");
            $db->bind(':name', $driver[0]);
            $db->bind(':phone', $driver[1]);
            $db->bind(':license_number', $driver[2]);
            $db->bind(':experience_years', $driver[3]);
            $db->bind(':rating', $driver[4]);
            $db->bind(':assigned_vehicle', $driver[5]);
            $db->execute();
        }
        echo "تم إضافة " . count($drivers) . " سائق<br>";
    } else {
        echo "السائقين موجودون بالفعل ($drivers_count سائق)<br>";
    }

    // إضافة رحلات تجريبية
    $db->query("SELECT COUNT(*) as count FROM transport_trips");
    $trips_count = $db->single()['count'];
    
    if ($trips_count == 0) {
        echo "إضافة الرحلات...<br>";
        
        // الحصول على فعالية موجودة أو إنشاء واحدة
        $db->query("SELECT id FROM events LIMIT 1");
        $event = $db->single();
        
        if (!$event) {
            // إنشاء فعالية تجر��بية
            $db->query("INSERT INTO events (title, description, date_time, location, price) VALUES ('فعالية تجريبية', 'فعالية لاختبار نظام المواصلات', '2024-12-31 10:00:00', 'غزة', 50.00)");
            $db->execute();
            $event_id = $db->lastInsertId();
        } else {
            $event_id = $event['id'];
        }
        
        // الحصول على نقاط الانطلاق وأنواع المواصلات
        $db->query("SELECT id FROM transport_starting_points LIMIT 3");
        $starting_points = $db->resultSet();
        
        $db->query("SELECT id FROM transport_types LIMIT 3");
        $transport_types = $db->resultSet();
        
        if (!empty($starting_points) && !empty($transport_types)) {
            $trips = [
                [$starting_points[0]['id'], $transport_types[0]['id'], '2024-12-25 08:00:00', '2024-12-25 12:00:00', 75.00, 25],
                [$starting_points[0]['id'], $transport_types[1]['id'], '2024-12-25 09:00:00', '2024-12-25 13:00:00', 60.00, 12],
                [$starting_points[1]['id'], $transport_types[0]['id'], '2024-12-25 08:30:00', '2024-12-25 12:30:00', 90.00, 30],
                [$starting_points[1]['id'], $transport_types[2]['id'], '2024-12-25 10:00:00', '2024-12-25 14:00:00', 120.00, 8],
                [$starting_points[2]['id'], $transport_types[1]['id'], '2024-12-25 09:30:00', '2024-12-25 13:30:00', 100.00, 15]
            ];
            
            foreach ($trips as $trip) {
                $db->query("INSERT INTO transport_trips (event_id, starting_point_id, transport_type_id, departure_time, arrival_time, price, total_seats, available_seats, is_active) VALUES (:event_id, :starting_point_id, :transport_type_id, :departure_time, :arrival_time, :price, :total_seats, :available_seats, 1)");
                $db->bind(':event_id', $event_id);
                $db->bind(':starting_point_id', $trip[0]);
                $db->bind(':transport_type_id', $trip[1]);
                $db->bind(':departure_time', $trip[2]);
                $db->bind(':arrival_time', $trip[3]);
                $db->bind(':price', $trip[4]);
                $db->bind(':total_seats', $trip[5]);
                $db->bind(':available_seats', $trip[5]);
                $db->execute();
            }
            echo "تم إضافة " . count($trips) . " رحلة<br>";
        }
    } else {
        echo "الرحلات موجودة بالفعل ($trips_count رحلة)<br>";
    }

    // إضافة حجوزات تجريبية
    $db->query("SELECT COUNT(*) as count FROM transport_bookings");
    $bookings_count = $db->single()['count'];
    
    if ($bookings_count == 0) {
        echo "إضافة الحجوزات...<br>";
        
        // الحصول على رحلات موجودة
        $db->query("SELECT id, event_id, price FROM transport_trips LIMIT 3");
        $trips = $db->resultSet();
        
        if (!empty($trips)) {
            $bookings = [
                [$trips[0]['id'], $trips[0]['event_id'], 'محمد أحمد الفلسطيني', '0599111222', 2, $trips[0]['price'] * 2],
                [$trips[0]['id'], $trips[0]['event_id'], 'فاطمة علي قاسم', '0598333444', 1, $trips[0]['price']],
                [count($trips) > 1 ? $trips[1]['id'] : $trips[0]['id'], count($trips) > 1 ? $trips[1]['event_id'] : $trips[0]['event_id'], 'عبدالله محمود النجار', '0597555666', 3, (count($trips) > 1 ? $trips[1]['price'] : $trips[0]['price']) * 3]
            ];
            
            foreach ($bookings as $booking) {
                $booking_code = 'TB' . str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
                
                $db->query("INSERT INTO transport_bookings (trip_id, event_id, passenger_name, passenger_phone, seats, total_amount, payment_method, payment_status, booking_code, status) VALUES (:trip_id, :event_id, :passenger_name, :passenger_phone, :seats, :total_amount, 'bank_transfer', 'confirmed', :booking_code, 'confirmed')");
                $db->bind(':trip_id', $booking[0]);
                $db->bind(':event_id', $booking[1]);
                $db->bind(':passenger_name', $booking[2]);
                $db->bind(':passenger_phone', $booking[3]);
                $db->bind(':seats', $booking[4]);
                $db->bind(':total_amount', $booking[5]);
                $db->bind(':booking_code', $booking_code);
                $db->execute();
            }
            echo "تم إضافة " . count($bookings) . " حجز<br>";
        }
    } else {
        echo "الحجوزات موجودة بالفعل ($bookings_count حجز)<br>";
    }

    // إضافة إشعارات تجريبية
    $db->query("SELECT COUNT(*) as count FROM notifications");
    $notifications_count = $db->single()['count'];
    
    if ($notifications_count == 0) {
        echo "إضافة الإشعارات...<br>";
        $notifications = [
            ['حجز جديد', 'تم استلام حجز جديد للمواصلات', 'info'],
            ['رحلة ملغاة', 'تم إلغاء رحلة غزة - رام الله', 'warning'],
            ['دفعة مؤكدة', 'تم تأكيد دفعة حجز المواصلات', 'success']
        ];
        
        foreach ($notifications as $notification) {
            $db->query("INSERT INTO notifications (user_id, title, message, type, is_read) VALUES (NULL, :title, :message, :type, 0)");
            $db->bind(':title', $notification[0]);
            $db->bind(':message', $notification[1]);
            $db->bind(':type', $notification[2]);
            $db->execute();
        }
        echo "تم إضافة " . count($notifications) . " إشعار<br>";
    } else {
        echo "الإشعارات موجودة بالفعل ($notifications_count إشعار)<br>";
    }

    echo "<br><strong>تم الانتهاء من إضافة البيانات التجريبية!</strong><br>";

} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}

echo "<br><a href='check_data.php'>فحص البيانات</a> | <a href='dashboard.php'>لوحة التحكم</a>";
?>