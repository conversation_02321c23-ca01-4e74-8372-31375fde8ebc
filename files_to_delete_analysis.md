# 🗑️ تحليل الملفات المقترحة للحذف

## ملفات الاختبار والتطوير (آمنة للحذف):

### ملفات الاختبار:
- test_all_functions.php
- test_all_pages.php
- test_basic.php
- test_booking_flow.php
- test_checkout_redirect.php
- test_full_flow.php
- test_login_redirect.php
- test_my_tickets.php
- test_notifications.php
- test_payment_process.php
- test_payment_processing.php
- test_process_booking.php
- test_simple.php
- test_transport_bookings.php
- test_transport_checkout.php
- test_transport_flow.php
- test_transport_integration.php
- test_transport_links.php
- test_transport_system.php
- test_unified_checkout.php
- test_website_complete.php

### ملفات التصحيح:
- debug_booking.log
- debug_payment_process.php
- debug_process_booking.php
- debug_simple.php
- debug_site.php

### ملفات الإصلاح (بعد التأكد من عملها):
- add_original_price_column.php
- add_trips_for_existing_points.php
- check_and_fix_users_table.php
- check_db_vs_code.php
- check_tables.php
- check_trips_table.php
- fix_all_pages.php
- fix_db_columns.php
- fix_duplicate_functions.php
- fix_missing_columns.php
- fix_notifications_table.php
- fix_tickets_table.php
- fix_transport_trips.php

### ملفات الإعداد المؤقتة:
- setup_admin_permissions.php
- setup_notifications.php
- setup_notifications_system.php
- create_notifications_table.sql
- create_sample_transport_data.php
- create_super_admin.php
- create_transport_table.php

### ملفات التنظيف:
- cleanup_project.php
- clear_errors.php
- remove_telegram_columns.php
- update_payment_security.php

### ملفات أخرى:
- simulate_booking_form.php
- final_website_test.php
- new1.zip (نسخة مضغوطة)

## ملفات قد تكون مكررة (تحتاج مراجعة):

### صفحات مكررة:
- about-simple.php (إذا كان about.php يعمل)
- payment-processing.php (مقابل payment-process.php)

### ملفات PayPal القديمة:
- paypal-loading.php
- paypal-login.php
- verify-paypal.php
- verify-paypal-ajax.php

## ملفات المراقبة والسجلات:

### ملفات السجلات:
- view_debug_log.php
- view_error_log.php
- logs/error.log
- logs/telegram_cleanup_report.json

## الملفات الأساسية (يجب الاحتفاظ بها):

### الصفحات الرئيسية:
- index.php
- events.php
- event-details.php
- login.php
- register.php
- logout.php
- checkout.php
- my-tickets.php
- notifications.php
- profile.php
- about.php
- contact.php
- privacy-policy.php
- preferences.php
- security.php
- invoices.php
- payment-methods.php
- payment-success.php
- payment-failed.php
- forgot-password.php
- reset-password.php

### المجلدات الأساسية:
- admin/ (لوحة الإدارة)
- transport/ (نظام المواصلات)
- includes/ (الملفات المشتركة)
- config/ (إعدادات قاعدة البيانات)
- assets/ (الملفات الثابتة)
- lang/ (ملفات الترجمة)
- photo/ (الصور)
- uploads/ (الملفات المرفوعة)

## توصيات:

1. **احذف ملفات الاختبار والتطوير** - آمنة 100%
2. **احذف ملفات الإصلاح** - بعد التأكد من عمل النظام
3. **راجع الملفات المكررة** - احتفظ بالأحدث
4. **احتفظ بملفات السجلات** - للمراقبة
5. **اعمل نسخة احتياطية** - قبل الحذف

## إجمالي الملفات المقترحة للحذف: ~50 ملف
