<?php
require_once '../includes/init.php';

$db = new Database();

try {
    echo "<h2>إضافة بيانات تجريبية لنظام المواصلات</h2>";

    // إضافة بيانات تجريبية للسائقين
    $db->query("SELECT COUNT(*) as count FROM transport_drivers");
    $drivers_count = $db->single()['count'];

    if ($drivers_count == 0) {
        $drivers = [
            ['أحمد محمد الفلسطيني', '0599123456', 'DL001234', 'غزة - الرمال الجنوبية'],
            ['محمود علي أبو سالم', '0598765432', 'DL005678', 'خانيونس - الكتيبة'],
            ['خالد حسن النجار', '**********', 'DL009876', 'رفح - البرازيل'],
            ['يوسف عبدالله قاسم', '**********', 'DL004321', 'جباليا - النزلة'],
            ['عمر سالم الهور', '**********', 'DL008765', 'دير البلح - المغازي']
        ];

        foreach ($drivers as $driver) {
            $db->query("
                INSERT INTO transport_drivers (name, phone, license_number, address, status, is_active) 
                VALUES (:name, :phone, :license_number, :address, 'available', 1)
            ");
            $db->bind(':name', $driver[0]);
            $db->bind(':phone', $driver[1]);
            $db->bind(':license_number', $driver[2]);
            $db->bind(':address', $driver[3]);
            $db->execute();
        }
        echo "تم إضافة " . count($drivers) . " سائق تجريبي<br>";
    } else {
        echo "السائقين موجودون بالفعل ($drivers_count سائق)<br>";
    }

    // إضافة بيانات تجريبية للرحلات
    $db->query("SELECT COUNT(*) as count FROM transport_trips");
    $trips_count = $db->single()['count'];

    if ($trips_count == 0) {
        // الحصول على معرف فعالية موجودة
        $db->query("SELECT id FROM events LIMIT 1");
        $event = $db->single();
        
        if ($event) {
            $event_id = $event['id'];
            
            // الحصول على نقاط الانطلاق
            $db->query("SELECT id FROM transport_starting_points LIMIT 3");
            $starting_points = $db->resultSet();
            
            // الحصول على أنواع المواصلات
            $db->query("SELECT id FROM transport_types LIMIT 3");
            $transport_types = $db->resultSet();
            
            if (!empty($starting_points) && !empty($transport_types)) {
                $trips = [
                    [$starting_points[0]['id'], $transport_types[0]['id'], '2024-12-25 08:00:00', 75.00, 25],
                    [$starting_points[0]['id'], $transport_types[1]['id'], '2024-12-25 09:00:00', 60.00, 12],
                    [$starting_points[1]['id'], $transport_types[0]['id'], '2024-12-25 08:30:00', 90.00, 30],
                    [$starting_points[1]['id'], $transport_types[2]['id'], '2024-12-25 10:00:00', 120.00, 4],
                    [$starting_points[2]['id'], $transport_types[1]['id'], '2024-12-25 09:30:00', 100.00, 15]
                ];

                foreach ($trips as $trip) {
                    $db->query("
                        INSERT INTO transport_trips (
                            starting_point_id, event_id, transport_type_id, 
                            departure_time, price, available_seats, total_seats, is_active
                        ) VALUES (
                            :starting_point_id, :event_id, :transport_type_id,
                            :departure_time, :price, :available_seats, :available_seats, 1
                        )
                    ");
                    $db->bind(':starting_point_id', $trip[0]);
                    $db->bind(':event_id', $event_id);
                    $db->bind(':transport_type_id', $trip[1]);
                    $db->bind(':departure_time', $trip[2]);
                    $db->bind(':price', $trip[3]);
                    $db->bind(':available_seats', $trip[4]);
                    $db->execute();
                }
                echo "تم إضافة " . count($trips) . " رحلة تجريبية<br>";
            } else {
                echo "لا توجد نقاط انطلاق أو أنواع مواصلات لإضافة الرحلات<br>";
            }
        } else {
            echo "لا توجد فعاليات لإضافة الرحلات<br>";
        }
    } else {
        echo "الرحلات موجودة بالفعل ($trips_count رحلة)<br>";
    }

    // إضافة بيانات تجريبية للحجوزات
    $db->query("SELECT COUNT(*) as count FROM transport_bookings");
    $bookings_count = $db->single()['count'];

    if ($bookings_count == 0) {
        // الحصول على رحلة موجودة
        $db->query("SELECT id, event_id, price FROM transport_trips LIMIT 3");
        $trips = $db->resultSet();
        
        if (!empty($trips)) {
            $bookings = [
                [$trips[0]['id'], $trips[0]['event_id'], 'محمد أحمد الفلسطيني', '0599111222', 2, $trips[0]['price'] * 2],
                [$trips[0]['id'], $trips[0]['event_id'], 'فاطمة علي قاسم', '0598333444', 1, $trips[0]['price']],
                [$trips[1]['id'], $trips[1]['event_id'], 'عبدالله محمود النجار', '0597555666', 3, $trips[1]['price'] * 3]
            ];

            foreach ($bookings as $booking) {
                $booking_code = 'TB' . str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
                
                $db->query("
                    INSERT INTO transport_bookings (
                        trip_id, event_id, customer_name, customer_phone, 
                        seats_count, total_amount, payment_method, payment_status, 
                        booking_code, status
                    ) VALUES (
                        :trip_id, :event_id, :customer_name, :customer_phone,
                        :seats_count, :total_amount, 'bank_transfer', 'confirmed',
                        :booking_code, 'confirmed'
                    )
                ");
                $db->bind(':trip_id', $booking[0]);
                $db->bind(':event_id', $booking[1]);
                $db->bind(':customer_name', $booking[2]);
                $db->bind(':customer_phone', $booking[3]);
                $db->bind(':seats_count', $booking[4]);
                $db->bind(':total_amount', $booking[5]);
                $db->bind(':booking_code', $booking_code);
                $db->execute();
            }
            echo "تم إضافة " . count($bookings) . " حجز تجريبي<br>";
        } else {
            echo "لا توجد رحلات لإضافة الحجوزات<br>";
        }
    } else {
        echo "الحجوزات موجودة بالفعل ($bookings_count حجز)<br>";
    }

    echo "<br><strong>تم إضافة البيانات التجريبية بنجاح!</strong><br>";
    echo "<a href='dashboard.php'>العودة إلى لوحة التحكم</a> | ";
    echo "<a href='trips.php'>عرض الرحلات</a> | ";
    echo "<a href='drivers.php'>عرض السائقين</a> | ";
    echo "<a href='bookings.php'>عرض الحجوزات</a>";

} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
