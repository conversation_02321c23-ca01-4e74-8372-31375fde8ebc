// Custom variables
$primary: #2c3e50;
$secondary: #e67e22;
$rtl: true;

// Bootstrap variable overrides
$enable-rounded: true;
$enable-shadows: true;
$enable-gradients: false;

// RTL adjustments
[dir="rtl"] {
  .dropdown-menu-end {
    right: auto !important;
    left: 0 !important;
  }
  
  .form-check {
    padding-right: 1.5em;
    padding-left: 0;
  }
}

// Custom styles
.navbar {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.btn-primary {
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  }
}

// Animations
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.5s ease-in;
}

// Header styles
.navbar {
  .nav-link {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      right: 0;
      width: 0;
      height: 2px;
      background: $secondary;
      transition: width 0.3s ease;
    }
    &:hover::after {
      width: 100%;
    }
  }
}

// Event cards
.event-card {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
  &-image {
    height: 200px;
    object-fit: cover;
  }
  &:hover {
    transform: translateY(-5px);
  }
}

// Footer styles
footer {
  .social-links {
    a {
      width: 40px;
      height: 40px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background: rgba(255,255,255,0.1);
      transition: all 0.3s ease;
      &:hover {
        background: $secondary;
        transform: scale(1.1);
      }
    }
  }
}

// Media queries
@media (max-width: 768px) {
  .navbar-collapse {
    padding: 1rem;
    background: rgba(255,255,255,0.95);
  }
  
  .event-card {
    margin-bottom: 1.5rem;
  }
}

@media (min-width: 992px) {
  .navbar {
    padding: 1rem 0;
  }
}